<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Free SEO Tools – Generate Meta Tags, SERP Preview, Robots.txt & More</title>
  <meta name="description" content="Boost your search visibility with our free SEO tools. Generate meta tags, preview SERP snippets, check keyword density, and create SEO-friendly content.">
  <meta name="keywords" content="seo tools, meta tag generator, robots.txt generator, sitemap generator, keyword density checker, serp preview, seo analyzer">
  <link rel="canonical" href="https://www.webtoolskit.org/p/seo-tools.html">

  <!-- Page-specific Open Graph Meta Tags -->
  <meta property="og:url" content="https://www.webtoolskit.org/p/seo-tools.html">
  <meta property="og:title" content="Free SEO Tools - Meta Tags, SERP Preview, Robots.txt Generator">
  <meta property="og:description" content="Boost your search visibility with our free SEO tools. Generate meta tags, preview SERP snippets, check keyword density, and create SEO-friendly content.">
  <meta property="og:image" content="https://www.webtoolskit.org/images/seo-tools-og.jpg">

  <!-- Page-specific Twitter Card Meta Tags -->
  <meta name="twitter:url" content="https://www.webtoolskit.org/p/seo-tools.html">
  <meta name="twitter:title" content="Free SEO Tools - Meta Tags, SERP Preview, Robots.txt Generator">
  <meta name="twitter:description" content="Boost your search visibility with our free SEO tools. Generate meta tags, preview SERP snippets, check keyword density, and create SEO-friendly content.">
  <meta name="twitter:image" content="https://www.webtoolskit.org/images/seo-tools-og.jpg">

  <!-- Enhanced Schema.org structured data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": ["WebPage", "CollectionPage"],
    "name": "Free SEO Tools – Generate Meta Tags, SERP Preview, Robots.txt & More",
    "description": "Boost your search visibility with our free SEO tools. Generate meta tags, preview SERP snippets, check keyword density, and create SEO-friendly content.",
    "url": "https://www.webtoolskit.org/p/seo-tools.html",
    "isAccessibleForFree": true,
    "datePublished": "2025-06-24",
    "dateModified": "2025-06-24",
    "author": { "@type": "Organization", "name": "WebToolsKit", "url": "https://www.webtoolskit.org", "logo": { "@type": "ImageObject", "url": "https://www.webtoolskit.org/images/logo.png" }},
    "publisher": { "@type": "Organization", "name": "WebToolsKit", "url": "https://www.webtoolskit.org" },
    "mainEntity": {
      "@type": "ItemList",
      "name": "Free SEO Tools Collection",
      "description": "A comprehensive collection of free online tools for SEO analysis, content optimization, and technical SEO.",
      "numberOfItems": 15,
      "itemListElement": [
        { "@type": "WebApplication", "position": 1, "name": "Meta Tag Generator", "description": "Generate SEO-optimized meta tags including title, description, and Open Graph tags.", "url": "https://www.webtoolskit.org/p/meta-tag-generator.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Meta title/description generation", "Open Graph tags", "SEO optimization"] },
        { "@type": "WebApplication", "position": 2, "name": "Robots.txt Generator", "description": "Create a robots.txt file to control how search engines crawl your website.", "url": "https://www.webtoolskit.org/p/robots-txt-generator.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Robots.txt creation", "Crawler directives (Allow/Disallow)", "Sitemap link inclusion"] },
        { "@type": "WebApplication", "position": 3, "name": "Sitemap Generator", "description": "Generate XML sitemaps to help search engines discover and index your content.", "url": "https://www.webtoolskit.org/p/sitemap-generator.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["XML sitemap generation", "URL discovery", "Search engine submission"] },
        { "@type": "WebApplication", "position": 4, "name": "Title Meta Description Checker", "description": "Analyze and optimize your page titles and meta descriptions for better SEO performance.", "url": "https://www.webtoolskit.org/p/title-meta-description-checker.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["SERP preview", "Length checker", "SEO analysis"] },
        { "@type": "WebApplication", "position": 5, "name": "Keyword Density Checker", "description": "Check keyword density and frequency to optimize your content for search engines.", "url": "https://www.webtoolskit.org/p/keyword-density-checker.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Keyword density analysis", "Content optimization", "Frequency count"] },
        { "@type": "WebApplication", "position": 6, "name": "URL SEO Analyzer", "description": "Analyze URL structure and get recommendations for SEO-friendly URLs.", "url": "https://www.webtoolskit.org/p/url-seo-analyzer.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["URL structure analysis", "SEO best practices", "Readability check"] },
        { "@type": "WebApplication", "position": 7, "name": "Open Graph Tag Generator", "description": "Generate Open Graph meta tags for better social media sharing and visibility.", "url": "https://www.webtoolskit.org/p/open-graph-tag-generator.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["OG tag generation", "Social media preview", "Facebook/Twitter cards"] },
        { "@type": "WebApplication", "position": 8, "name": "Canonical URL Generator", "description": "Generate canonical URL tags to prevent duplicate content issues and improve SEO.", "url": "https://www.webtoolskit.org/p/canonical-url-generator.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Canonical tag creation", "Duplicate content prevention", "SEO improvement"] },
        { "@type": "WebApplication", "position": 9, "name": "Alt Text Checker", "description": "Check and analyze alt text attributes for images to improve accessibility and SEO.", "url": "https://www.webtoolskit.org/p/alt-text-checker.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Image alt text analysis", "Accessibility check", "Image SEO"] },
        { "@type": "WebApplication", "position": 10, "name": "H1-H6 Heading Checker", "description": "Analyze heading structure and hierarchy for better SEO and content organization.", "url": "https://www.webtoolskit.org/p/h1-h6-heading-checker.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Heading structure analysis", "Content hierarchy check", "On-page SEO"] },
        { "@type": "WebApplication", "position": 11, "name": "Gmail Generator", "description": "Generate professional Gmail email templates and signatures for business use.", "url": "https://www.webtoolskit.org/p/gmail-generator.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Email template creation", "Professional signature design", "Business communication"] },
        { "@type": "WebApplication", "position": 12, "name": "Terms of Use Page Generator", "description": "Generate comprehensive terms of use pages for your website or application.", "url": "https://www.webtoolskit.org/p/terms-of-use-page-generator.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Legal document generation", "Website compliance", "Customizable templates"] },
        { "@type": "WebApplication", "position": 13, "name": "About Us Page Generator", "description": "Create professional About Us pages that build trust and connect with your audience.", "url": "https://www.webtoolskit.org/p/about-us-generator.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Page content generation", "Brand storytelling", "Trust building"] },
        { "@type": "WebApplication", "position": 14, "name": "Contact Us Page Generator", "description": "Generate effective contact pages with forms and contact information layouts.", "url": "https://www.webtoolskit.org/p/contact-us-page-generator.html", "applicationCaterory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Contact page creation", "Form builder", "Business information layout"] },
        { "@type": "WebApplication", "position": 15, "name": "Blogger Cleaning Template", "description": "Clean and optimize Blogger templates for better performance and SEO.", "url": "https://www.webtoolskit.org/p/blogger-cleaning-template.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Blogger template optimization", "Code cleaning", "Performance improvement"] }
      ]
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        { "@type": "ListItem", "position": 1, "name": "Home", "item": "https://www.webtoolskit.org" },
        { "@type": "ListItem", "position": 2, "name": "SEO Tools" }
      ]
    }
  }
  </script>

  <style>
    /* Duplicate CSS variables and base styles removed - inherit from main template */

    /* Page-specific styles only */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 8px 16px;
    }

    .page-header {
      text-align: center;
      margin-bottom: 20px;
    }

    .page-title {
      font-size: 36px;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 10px;
      line-height: 1.2;
    }

    .page-description {
      font-size: 1.1rem;
      color: var(--text-color-light);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.5;
    }

    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
      justify-items: center;
    }

    .tool-card {
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 10px;
      padding: 18px;
      text-align: center;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      position: relative;
      overflow: hidden;
      cursor: pointer;
    }

    .tool-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      opacity: 0;
      transition: opacity 0.4s ease;
    }

    .tool-card::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .tool-card:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 12px 35px rgba(0,0,0,0.2);
      border-color: var(--primary-color);
    }

    .tool-card:hover::before {
      opacity: 1;
    }

    .tool-card:hover::after {
      left: 100%;
    }

    .tool-icon {
      width: 56px;
      height: 56px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
      font-size: 22px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .tool-icon::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .tool-card:hover .tool-icon {
      transform: scale(1.1) rotate(5deg);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    .tool-card:hover .tool-icon::before {
      opacity: 1;
    }

    /* Distinctive Icon Colors for SEO Tools */
    .icon-meta-generator { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; }
    .icon-robots-generator { background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; }
    .icon-sitemap-generator { background: linear-gradient(135deg, #EC4899, #DB2777); color: white; }
    .icon-title-checker { background: linear-gradient(135deg, #10B981, #059669); color: white; }
    .icon-keyword-density { background: linear-gradient(135deg, #F59E0B, #D97706); color: white; }
    .icon-url-analyzer { background: linear-gradient(135deg, #6366F1, #4F46E5); color: white; }
    .icon-alt-checker { background: linear-gradient(135deg, #0EA5E9, #0284C7); color: white; }
    .icon-heading-checker { background: linear-gradient(135deg, #4F46E5, #4338CA); color: white; }
    .icon-og-generator { background: linear-gradient(135deg, #EF4444, #DC2626); color: white; }
    .icon-canonical-generator { background: linear-gradient(135deg, #14B8A6, #0D9488); color: white; }
    .icon-gmail-generator { background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; }
    .icon-page-generator { background: linear-gradient(135deg, #F97316, #EA580C); color: white; }
    .icon-terms-generator { background: linear-gradient(135deg, #06B6D4, #0891B2); color: white; }
    .icon-about-generator { background: linear-gradient(135deg, #84CC16, #65A30D); color: white; }
    .icon-contact-generator { background: linear-gradient(135deg, #A855F7, #9333EA); color: white; }
    .icon-blogger-template { background: linear-gradient(135deg, #3B82F6, #2563EB); color: white; }

    .tool-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 8px;
    }

    .tool-description {
      color: var(--text-color-light);
      font-size: 14px;
      margin-bottom: 12px;
      line-height: 1.4;
    }

    .tool-link {
      display: inline-block;
      background: var(--primary-color);
      color: #ffffff !important;
      text-decoration: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2px solid var(--primary-color);
      box-shadow: 0 2px 8px rgba(0, 71, 171, 0.2);
      position: relative;
      overflow: hidden;
    }

    .tool-link::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    .tool-link:hover {
      background: #003d96;
      border-color: #003d96;
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
      color: #ffffff !important;
    }

    .tool-link:hover::before {
      left: 100%;
    }

    [data-theme="dark"] .tool-link {
      background: #60a5fa;
      border-color: #60a5fa;
      color: #ffffff !important;
      box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
    }

    [data-theme="dark"] .tool-link:hover {
      background: #3b82f6;
      border-color: #3b82f6;
      box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
      color: #ffffff !important;
    }

    /* Mobile Optimization with Scrollable Icons */
    @media (max-width: 768px) {
      .container { padding: 6px 12px; }
      .page-header { margin-bottom: 16px; }
      .page-title { font-size: 28px; margin-bottom: 8px; }
      .page-description { font-size: 1rem; padding: 0 8px; }

      .tools-grid { display: block; overflow-x: unset; padding: 8px 0; }
      .tool-card { width: 100%; margin: 0 0 12px 0; border-radius: 14px; padding: 16px; min-height: 80px; box-sizing: border-box; }
      .tool-card .tool-icon { width: 48px; height: 48px; margin: 0 auto 8px; font-size: 20px; }
      .tool-card .tool-title, .tool-card .tool-link { opacity: 1; transform: none; pointer-events: auto; }
      .tool-card .tool-description { opacity: 0; max-height: 0; overflow: hidden; margin: 0; transition: opacity 0.3s, max-height 0.3s; will-change: opacity, max-height; display: block; }
      .tool-card.show-description .tool-description { opacity: 1; max-height: 100px; margin-bottom: 10px; }
    }
    @media (max-width: 480px) {
      .tool-card { min-width: 200px; max-width: 95vw; padding: 10px; }
      .tool-card .tool-icon { width: 40px; height: 40px; font-size: 18px; }
    }
    @media (max-width: 320px) {
      .tool-card { min-width: 140px; padding: 6px; }
      .tool-card .tool-icon { width: 32px; height: 32px; font-size: 15px; }
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="page-header">
      <h1 id="main-title" class="page-title">Free SEO Tools – Meta Tags, SERP Preview, Robots.txt Generator</h1>
      <p class="page-description">Boost your search visibility with our free SEO tools. Generate meta tags, preview SERP snippets, check keyword density, and create SEO-friendly content.</p>
    </header>

    <main id="main-content" role="main" aria-labelledby="main-title">
      <section class="tools-section" aria-labelledby="tools-section-title">
        <h2 id="tools-section-title" class="sr-only">Available SEO Tools</h2>
        <div class="tools-grid" role="list">
          <!-- Meta Tag Generator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-meta-generator" aria-hidden="true"><i class="fas fa-tags"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Meta Tag Generator</h3>
              <p class="tool-description">Generate SEO-optimized meta tags including title, description, and Open Graph tags.</p>
              <a class="tool-link" href="/p/meta-tag-generator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Robots.txt Generator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-robots-generator" aria-hidden="true"><i class="fas fa-robot"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Robots.txt Generator</h3>
              <p class="tool-description">Create a robots.txt file to control how search engines crawl your website.</p>
              <a class="tool-link" href="/p/robots-txt-generator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Sitemap Generator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-sitemap-generator" aria-hidden="true"><i class="fas fa-sitemap"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Sitemap Generator</h3>
              <p class="tool-description">Generate XML sitemaps to help search engines discover and index your content.</p>
              <a class="tool-link" href="/p/sitemap-generator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Title Meta Description Checker -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-title-checker" aria-hidden="true"><i class="fas fa-search"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Title Meta Description Checker</h3>
              <p class="tool-description">Analyze and optimize your page titles and meta descriptions for better SEO performance.</p>
              <a class="tool-link" href="/p/title-meta-description-checker.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Keyword Density Checker -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-keyword-density" aria-hidden="true"><i class="fas fa-percentage"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Keyword Density Checker</h3>
              <p class="tool-description">Check keyword density and frequency to optimize your content for search engines.</p>
              <a class="tool-link" href="/p/keyword-density-checker.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- URL SEO Analyzer -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-url-analyzer" aria-hidden="true"><i class="fas fa-chart-line"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">URL SEO Analyzer</h3>
              <p class="tool-description">Analyze URL structure and get recommendations for SEO-friendly URLs.</p>
              <a class="tool-link" href="/p/url-seo-analyzer.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Open Graph Tag Generator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-og-generator" aria-hidden="true"><i class="fab fa-facebook"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Open Graph Tag Generator</h3>
              <p class="tool-description">Generate Open Graph meta tags for better social media sharing and visibility.</p>
              <a class="tool-link" href="/p/open-graph-tag-generator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Canonical URL Generator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-canonical-generator" aria-hidden="true"><i class="fas fa-link"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Canonical URL Generator</h3>
              <p class="tool-description">Generate canonical URL tags to prevent duplicate content issues and improve SEO.</p>
              <a class="tool-link" href="/p/canonical-url-generator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Alt Text Checker -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-alt-checker" aria-hidden="true"><i class="fas fa-image"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Alt Text Checker</h3>
              <p class="tool-description">Check and analyze alt text attributes for images to improve accessibility and SEO.</p>
              <a class="tool-link" href="/p/alt-text-checker.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- H1-H6 Heading Checker -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-heading-checker" aria-hidden="true"><i class="fas fa-heading"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">H1-H6 Heading Checker</h3>
              <p class="tool-description">Analyze heading structure and hierarchy for better SEO and content organization.</p>
              <a class="tool-link" href="/p/h1-h6-heading-checker.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Gmail Generator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-gmail-generator" aria-hidden="true"><i class="fas fa-envelope"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Gmail Generator</h3>
              <p class="tool-description">Generate professional Gmail email templates and signatures for business use.</p>
              <a class="tool-link" href="/p/gmail-generator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Terms of Use Page Generator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-terms-generator" aria-hidden="true"><i class="fas fa-file-contract"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Terms of Use Page Generator</h3>
              <p class="tool-description">Generate comprehensive terms of use pages for your website or application.</p>
              <a class="tool-link" href="/p/terms-of-use-page-generator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- About Us Page Generator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-about-generator" aria-hidden="true"><i class="fas fa-info-circle"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">About Us Page Generator</h3>
              <p class="tool-description">Create professional About Us pages that build trust and connect with your audience.</p>
              <a class="tool-link" href="/p/about-us-generator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Contact Us Page Generator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-contact-generator" aria-hidden="true"><i class="fas fa-phone"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Contact Us Page Generator</h3>
              <p class="tool-description">Generate effective contact pages with forms and contact information layouts.</p>
              <a class="tool-link" href="/p/contact-us-page-generator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Blogger Cleaning Template -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-blogger-template" aria-hidden="true"><i class="fab fa-blogger"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Blogger Cleaning Template</h3>
              <p class="tool-description">Clean and optimize Blogger templates for better performance and SEO.</p>
              <a class="tool-link" href="/p/blogger-cleaning-template.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
        </div>
      </section>
    </main>
  </div>
  
  <script>
    // --- SIMPLIFIED: Expand card on tap for mobile ---
    document.addEventListener('DOMContentLoaded', function() {
      if (window.innerWidth > 768) return;
      const toolCards = document.querySelectorAll('.tool-card');
      toolCards.forEach(card => {
        card.addEventListener('click', function(e) {
          // If the click is on a button/link, let it work normally
          if (e.target.closest('.tool-link')) return;
          // Always show description, never hide
          toolCards.forEach(c => c.classList.remove('show-description'));
          this.classList.add('show-description');
        });
      });
      // Ensure tool-link buttons work on mobile
      const toolLinks = document.querySelectorAll('.tool-link');
      toolLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          // Let the link work as normal
          e.stopPropagation();
        });
        link.addEventListener('touchend', function(e) {
          // Let the link work as normal
          e.stopPropagation();
        }, { passive: false });
      });
    });
  </script>

</body>
</html>